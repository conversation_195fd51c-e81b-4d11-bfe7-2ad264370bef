---
type: "always_apply"
---

# 开发规范与编码标准

## 🏆 核心原则

### 1. 约定大于配置 (Convention over Configuration)

- **优先使用框架和库的默认约定**，避免过度自定义配置
- **架构设计最小配置原则**：
  - 理想状态下开发者无需传入任何配置即可运行在"最佳配置"
  - 只暴露确实必要的配置选项，隐藏技术实现细节
  - 提供合理的默认值，让 80% 的使用场景开箱即用
  - 配置项应该是业务需求驱动，而非技术实现驱动

### 2. 保持代码精简 (Keep It Simple)

- **拒绝过度设计**：不要为了"未来可能的需求"而添加复杂抽象
- **单一职责原则**：每个函数、组件只做一件事
- **删除死代码**：及时清理不用的代码、注释和依赖
- **避免深层嵌套**：超过 3 层嵌套时考虑重构

### 3. 类型安全优先 (Type Safety First)

- **避免 `any` 类型**：使用具体类型
- **避免类型断言**：
- **利用类型推导**：让 TypeScript 自动推导类型，减少冗余标注

### 4. 尽早报错退出 (Fail Fast Principle)

- **参数校验在函数开头**：不符合预期立即抛出错误
- **避免兜底逻辑**：不要默默处理错误情况
- **明确的错误信息**：错误要说明原因和解决方案

### 5. 重构时保持简洁 (Clean Refactoring)

- **删除旧代码**：重构时完全移除被替换的代码
- **不做兼容层**：避免为了向后兼容而保留旧接口
- **不做降级逻辑**：新功能就用新方式，不要混合新旧实现
- **一次性迁移**：完整重构而不是渐进式兼容

### 6. 真实满足需求 (Genuine Solutions)

- **理解真实需求**：不要基于假设实现功能
- **避免 hack 手段**：使用正确的方式解决问题
- **直接解决问题**：不要绕弯子或临时方案
- **可维护的解决方案**：考虑长期维护成本

### 7. 复用优先原则 (Reuse First)

- **全局搜索已有实现**：开发前先搜索相似功能
- **扩展现有工具**：优先扩展而不是重新创建
- **统一的工具函数**：相同功能使用相同的工具
- **共享类型定义**：避免重复定义相同的类型

### 8. 合理组织文件 (Reasonable File Organization)

- **按功能分组**：相关功能放在一起
- **避免过深嵌套**：不超过 3 层目录
- **使用索引文件**：统一导出模块内容
- **保持文件整洁**：每个文件不超过 500 行

---

> 记住：简单、直接、安全。这三个词概括了我们所有的开发原则。
