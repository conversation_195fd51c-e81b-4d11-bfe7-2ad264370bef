/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

import { Route as rootRouteImport } from './routes/__root'
import { Route as RegisterRouteImport } from './routes/register'
import { Route as LoginRouteImport } from './routes/login'
import { Route as AppRouteImport } from './routes/app'
import { Route as IndexRouteImport } from './routes/index'
import { Route as AppZustandDemoRouteImport } from './routes/app/zustand-demo'
import { Route as AppTodosRouteImport } from './routes/app/todos'
import { Route as AppCounterRouteImport } from './routes/app/counter'

const RegisterRoute = RegisterRouteImport.update({
  id: '/register',
  path: '/register',
  getParentRoute: () => rootRouteImport,
} as any)
const LoginRoute = LoginRouteImport.update({
  id: '/login',
  path: '/login',
  getParentRoute: () => rootRouteImport,
} as any)
const AppRoute = AppRouteImport.update({
  id: '/app',
  path: '/app',
  getParentRoute: () => rootRouteImport,
} as any)
const IndexRoute = IndexRouteImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => rootRouteImport,
} as any)
const AppZustandDemoRoute = AppZustandDemoRouteImport.update({
  id: '/zustand-demo',
  path: '/zustand-demo',
  getParentRoute: () => AppRoute,
} as any)
const AppTodosRoute = AppTodosRouteImport.update({
  id: '/todos',
  path: '/todos',
  getParentRoute: () => AppRoute,
} as any)
const AppCounterRoute = AppCounterRouteImport.update({
  id: '/counter',
  path: '/counter',
  getParentRoute: () => AppRoute,
} as any)

export interface FileRoutesByFullPath {
  '/': typeof IndexRoute
  '/app': typeof AppRouteWithChildren
  '/login': typeof LoginRoute
  '/register': typeof RegisterRoute
  '/app/counter': typeof AppCounterRoute
  '/app/todos': typeof AppTodosRoute
  '/app/zustand-demo': typeof AppZustandDemoRoute
}
export interface FileRoutesByTo {
  '/': typeof IndexRoute
  '/app': typeof AppRouteWithChildren
  '/login': typeof LoginRoute
  '/register': typeof RegisterRoute
  '/app/counter': typeof AppCounterRoute
  '/app/todos': typeof AppTodosRoute
  '/app/zustand-demo': typeof AppZustandDemoRoute
}
export interface FileRoutesById {
  __root__: typeof rootRouteImport
  '/': typeof IndexRoute
  '/app': typeof AppRouteWithChildren
  '/login': typeof LoginRoute
  '/register': typeof RegisterRoute
  '/app/counter': typeof AppCounterRoute
  '/app/todos': typeof AppTodosRoute
  '/app/zustand-demo': typeof AppZustandDemoRoute
}
export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath
  fullPaths:
    | '/'
    | '/app'
    | '/login'
    | '/register'
    | '/app/counter'
    | '/app/todos'
    | '/app/zustand-demo'
  fileRoutesByTo: FileRoutesByTo
  to:
    | '/'
    | '/app'
    | '/login'
    | '/register'
    | '/app/counter'
    | '/app/todos'
    | '/app/zustand-demo'
  id:
    | '__root__'
    | '/'
    | '/app'
    | '/login'
    | '/register'
    | '/app/counter'
    | '/app/todos'
    | '/app/zustand-demo'
  fileRoutesById: FileRoutesById
}
export interface RootRouteChildren {
  IndexRoute: typeof IndexRoute
  AppRoute: typeof AppRouteWithChildren
  LoginRoute: typeof LoginRoute
  RegisterRoute: typeof RegisterRoute
}

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/register': {
      id: '/register'
      path: '/register'
      fullPath: '/register'
      preLoaderRoute: typeof RegisterRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/login': {
      id: '/login'
      path: '/login'
      fullPath: '/login'
      preLoaderRoute: typeof LoginRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/app': {
      id: '/app'
      path: '/app'
      fullPath: '/app'
      preLoaderRoute: typeof AppRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/': {
      id: '/'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof IndexRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/app/zustand-demo': {
      id: '/app/zustand-demo'
      path: '/zustand-demo'
      fullPath: '/app/zustand-demo'
      preLoaderRoute: typeof AppZustandDemoRouteImport
      parentRoute: typeof AppRoute
    }
    '/app/todos': {
      id: '/app/todos'
      path: '/todos'
      fullPath: '/app/todos'
      preLoaderRoute: typeof AppTodosRouteImport
      parentRoute: typeof AppRoute
    }
    '/app/counter': {
      id: '/app/counter'
      path: '/counter'
      fullPath: '/app/counter'
      preLoaderRoute: typeof AppCounterRouteImport
      parentRoute: typeof AppRoute
    }
  }
}

interface AppRouteChildren {
  AppCounterRoute: typeof AppCounterRoute
  AppTodosRoute: typeof AppTodosRoute
  AppZustandDemoRoute: typeof AppZustandDemoRoute
}

const AppRouteChildren: AppRouteChildren = {
  AppCounterRoute: AppCounterRoute,
  AppTodosRoute: AppTodosRoute,
  AppZustandDemoRoute: AppZustandDemoRoute,
}

const AppRouteWithChildren = AppRoute._addFileChildren(AppRouteChildren)

const rootRouteChildren: RootRouteChildren = {
  IndexRoute: IndexRoute,
  AppRoute: AppRouteWithChildren,
  LoginRoute: LoginRoute,
  RegisterRoute: RegisterRoute,
}
export const routeTree = rootRouteImport
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>()
