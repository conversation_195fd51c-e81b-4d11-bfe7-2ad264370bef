import { createFileRoute } from '@tanstack/react-router';
import { Link } from '@tanstack/react-router';
import { useState, useEffect } from 'react';
import {
  useCounterStore,
  useUserActions,
  useUser,
  useIsAuthenticated,
  useThemeActions,
  useActualTheme,
  useMerchantActions,
  useFilteredMerchants,
  useMerchantStats,
  useMerchantFilters,
  subscribeMerchantChanges,
} from '@/stores';
import type { User, Merchant } from '@/types';
import { UserRole, MerchantStatus } from '@/types';

function ZustandDemoPage() {
  // Counter store
  const { count, increment, decrement, reset } = useCounterStore();

  // User store
  const user = useUser();
  const isAuthenticated = useIsAuthenticated();
  const { login, logout } = useUserActions();

  // Theme store
  const actualTheme = useActualTheme();
  const { toggleTheme } = useThemeActions();

  // Merchant store
  const merchants = useFilteredMerchants();
  const stats = useMerchantStats();
  const filters = useMerchantFilters();
  const { setMerchants, addMerchant, setFilters, clearFilters } = useMerchantActions();

  // Local state for demo
  const [newMerchantName, setNewMerchantName] = useState('');

  // 模拟登录用户
  const handleLogin = () => {
    const mockUser: User = {
      id: '1',
      username: 'demo_user',
      email: '<EMAIL>',
      role: UserRole.ADMIN,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };
    login(mockUser);
  };

  // 添加模拟商户
  const handleAddMerchant = () => {
    if (!newMerchantName.trim()) return;

    const newMerchant: Merchant = {
      id: Date.now().toString(),
      name: newMerchantName,
      status: MerchantStatus.ACTIVE,
      contactEmail: `${newMerchantName.toLowerCase().replace(/\s+/g, '')}@example.com`,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    addMerchant(newMerchant);
    setNewMerchantName('');
  };

  // 初始化模拟数据
  useEffect(() => {
    const mockMerchants: Merchant[] = [
      {
        id: '1',
        name: '示例商户 A',
        status: MerchantStatus.ACTIVE,
        contactEmail: '<EMAIL>',
        description: '这是一个示例商户',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      },
      {
        id: '2',
        name: '示例商户 B',
        status: MerchantStatus.PENDING,
        contactEmail: '<EMAIL>',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      },
    ];

    setMerchants(mockMerchants);

    // 订阅商户变化
    const unsubscribe = subscribeMerchantChanges();
    return unsubscribe;
  }, [setMerchants]);

  return (
    <div className="container mx-auto p-4">
      <div className="flex items-center mb-6">
        <Link to="/" className="btn btn-ghost mr-4">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            strokeWidth={1.5}
            stroke="currentColor"
            className="w-6 h-6"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              d="M10.5 19.5L3 12m0 0l7.5-7.5M3 12h18"
            />
          </svg>
          返回
        </Link>
        <h1 className="text-3xl font-bold">Zustand 完整示例</h1>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 计数器示例 */}
        <div className="card bg-base-100 shadow-xl">
          <div className="card-body">
            <h2 className="card-title">计数器状态</h2>
            <div className="text-center">
              <div className="text-4xl font-bold text-primary mb-4">{count}</div>
              <div className="flex gap-2 justify-center">
                <button onClick={decrement} className="btn btn-outline">
                  -
                </button>
                <button onClick={increment} className="btn btn-outline">
                  +
                </button>
                <button onClick={reset} className="btn btn-warning">
                  重置
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* 用户状态示例 */}
        <div className="card bg-base-100 shadow-xl">
          <div className="card-body">
            <h2 className="card-title">用户状态（持久化）</h2>
            {isAuthenticated ? (
              <div>
                <div className="mb-4">
                  <p>
                    <strong>用户名:</strong> {user?.username}
                  </p>
                  <p>
                    <strong>邮箱:</strong> {user?.email}
                  </p>
                  <p>
                    <strong>角色:</strong> {user?.role}
                  </p>
                </div>
                <button onClick={logout} className="btn btn-error">
                  登出
                </button>
              </div>
            ) : (
              <div>
                <p className="mb-4">未登录状态</p>
                <button onClick={handleLogin} className="btn btn-primary">
                  模拟登录
                </button>
              </div>
            )}
          </div>
        </div>

        {/* 主题状态示例 */}
        <div className="card bg-base-100 shadow-xl">
          <div className="card-body">
            <h2 className="card-title">主题状态</h2>
            <div>
              <p className="mb-4">
                当前主题: <span className="badge badge-primary">{actualTheme}</span>
              </p>
              <button onClick={toggleTheme} className="btn btn-outline">
                切换主题
              </button>
            </div>
          </div>
        </div>

        {/* 商户管理示例 */}
        <div className="card bg-base-100 shadow-xl">
          <div className="card-body">
            <h2 className="card-title">商户管理状态</h2>

            {/* 统计信息 */}
            <div className="stats stats-vertical lg:stats-horizontal shadow mb-4">
              <div className="stat">
                <div className="stat-title">总数</div>
                <div className="stat-value text-primary">{stats.total}</div>
              </div>
              <div className="stat">
                <div className="stat-title">活跃</div>
                <div className="stat-value text-success">{stats.active}</div>
              </div>
              <div className="stat">
                <div className="stat-title">待审核</div>
                <div className="stat-value text-warning">{stats.pending}</div>
              </div>
            </div>

            {/* 筛选器 */}
            <div className="flex gap-2 mb-4">
              <select
                className="select select-bordered select-sm"
                value={filters.status || ''}
                onChange={e =>
                  setFilters({ status: (e.target.value as MerchantStatus) || undefined })
                }
              >
                <option value="">所有状态</option>
                <option value={MerchantStatus.ACTIVE}>活跃</option>
                <option value={MerchantStatus.PENDING}>待审核</option>
                <option value={MerchantStatus.INACTIVE}>非活跃</option>
              </select>
              <input
                type="text"
                placeholder="搜索商户..."
                className="input input-bordered input-sm flex-1"
                value={filters.keyword || ''}
                onChange={e => setFilters({ keyword: e.target.value || undefined })}
              />
              <button onClick={clearFilters} className="btn btn-ghost btn-sm">
                清空
              </button>
            </div>

            {/* 添加商户 */}
            <div className="flex gap-2 mb-4">
              <input
                type="text"
                placeholder="商户名称"
                className="input input-bordered input-sm flex-1"
                value={newMerchantName}
                onChange={e => setNewMerchantName(e.target.value)}
              />
              <button onClick={handleAddMerchant} className="btn btn-primary btn-sm">
                添加
              </button>
            </div>

            {/* 商户列表 */}
            <div className="overflow-x-auto">
              <table className="table table-zebra table-sm">
                <thead>
                  <tr>
                    <th>名称</th>
                    <th>状态</th>
                  </tr>
                </thead>
                <tbody>
                  {merchants.map(merchant => (
                    <tr key={merchant.id}>
                      <td>{merchant.name}</td>
                      <td>
                        <span
                          className={`badge badge-sm ${
                            merchant.status === MerchantStatus.ACTIVE
                              ? 'badge-success'
                              : merchant.status === MerchantStatus.PENDING
                                ? 'badge-warning'
                                : 'badge-error'
                          }`}
                        >
                          {merchant.status}
                        </span>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>

      {/* Zustand 特性说明 */}
      <div className="mt-8 bg-base-200 p-6 rounded-lg">
        <h3 className="font-bold text-lg mb-4">本示例展示的 Zustand 特性</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <ul className="list-disc list-inside space-y-2">
            <li>
              <strong>基础状态管理:</strong> 计数器示例
            </li>
            <li>
              <strong>持久化存储:</strong> 用户状态自动保存到 localStorage
            </li>
            <li>
              <strong>中间件使用:</strong> immer、persist、subscribeWithSelector
            </li>
            <li>
              <strong>选择器优化:</strong> 避免不必要的重渲染
            </li>
          </ul>
          <ul className="list-disc list-inside space-y-2">
            <li>
              <strong>计算属性:</strong> 筛选后的商户列表和统计信息
            </li>
            <li>
              <strong>状态订阅:</strong> 监听商户列表变化
            </li>
            <li>
              <strong>复杂状态:</strong> 商户管理包含筛选、搜索等功能
            </li>
            <li>
              <strong>TypeScript 支持:</strong> 完整的类型定义
            </li>
          </ul>
        </div>
      </div>
    </div>
  );
}

export const Route = createFileRoute('/app/zustand-demo')({
  component: ZustandDemoPage,
});
