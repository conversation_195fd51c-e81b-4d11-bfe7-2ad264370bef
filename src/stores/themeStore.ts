import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';

// 主题类型
export type Theme = 'light' | 'dark' | 'auto';

// 主题状态接口
interface ThemeState {
  // 状态
  theme: Theme;
  systemTheme: 'light' | 'dark';

  // 操作
  setTheme: (theme: Theme) => void;
  setSystemTheme: (theme: 'light' | 'dark') => void;
  toggleTheme: () => void;
}

// 主题状态管理
export const useThemeStore = create<ThemeState>()(
  persist(
    (set, get) => ({
      // 初始状态
      theme: 'auto',
      systemTheme: 'light',

      // 设置主题
      setTheme: (theme: Theme) => {
        set({ theme });
        applyTheme(theme, get().systemTheme);
      },

      // 设置系统主题
      setSystemTheme: (systemTheme: 'light' | 'dark') => {
        set({ systemTheme });
        applyTheme(get().theme, systemTheme);
      },

      // 切换主题
      toggleTheme: () => {
        const currentTheme = get().theme;
        const newTheme = currentTheme === 'light' ? 'dark' : 'light';
        get().setTheme(newTheme);
      },
    }),
    {
      name: 'theme-storage',
      storage: createJSONStorage(() => localStorage),
    }
  )
);

// 应用主题到 DOM
function applyTheme(theme: Theme, systemTheme: 'light' | 'dark') {
  const actualTheme = theme === 'auto' ? systemTheme : theme;
  document.documentElement.setAttribute('data-theme', actualTheme);
}

// 初始化主题
export function initializeTheme() {
  const { theme, setSystemTheme } = useThemeStore.getState();

  // 监听系统主题变化
  const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
  const handleSystemThemeChange = (e: MediaQueryListEvent) => {
    setSystemTheme(e.matches ? 'dark' : 'light');
  };

  // 设置初始系统主题
  setSystemTheme(mediaQuery.matches ? 'dark' : 'light');

  // 监听系统主题变化
  mediaQuery.addEventListener('change', handleSystemThemeChange);

  // 应用当前主题
  applyTheme(theme, mediaQuery.matches ? 'dark' : 'light');

  // 返回清理函数
  return () => {
    mediaQuery.removeEventListener('change', handleSystemThemeChange);
  };
}

// 选择器函数
export const useTheme = () => useThemeStore(state => state.theme);
export const useSystemTheme = () => useThemeStore(state => state.systemTheme);
export const useThemeActions = () => {
  const setTheme = useThemeStore(state => state.setTheme);
  const toggleTheme = useThemeStore(state => state.toggleTheme);

  return { setTheme, toggleTheme };
};

// 计算当前实际主题
export const useActualTheme = () =>
  useThemeStore(state => (state.theme === 'auto' ? state.systemTheme : state.theme));
